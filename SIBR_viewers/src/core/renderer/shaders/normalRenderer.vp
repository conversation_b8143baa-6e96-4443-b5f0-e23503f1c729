/*
 * Copyright (C) 2020, Inria
 * GRAPHDECO research group, https://team.inria.fr/graphdeco
 * All rights reserved.
 *
 * This software is free for non-commercial, research and evaluation use 
 * under the terms of the LICENSE.md file.
 *
 * For <NAME_EMAIL> and/or <PERSON><PERSON>@inria.fr
 */


#version 420

uniform mat4 MVP;
uniform mat4 M;
uniform mat4 V;
uniform bool imSpaceNormals;

layout(location = 0) in vec3 in_vertex;
layout(location = 3) in vec3 in_normal;

out vec3 GtoF_normal;

void main(void) {
	gl_Position = MVP * vec4(in_vertex,1.0);

	if(imSpaceNormals)
		GtoF_normal = normalize((inverse(transpose(V*M)) * vec4(in_normal,0.0)).xyz);
	else
		GtoF_normal = normalize((inverse(transpose(M)) * vec4(in_normal,0.0)).xyz);
}

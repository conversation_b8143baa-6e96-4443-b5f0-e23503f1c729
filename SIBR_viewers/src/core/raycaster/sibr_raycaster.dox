/*
 * Copyright (C) 2020, Inria
 * GRAPHDECO research group, https://team.inria.fr/graphdeco
 * All rights reserved.
 *
 * This software is free for non-commercial, research and evaluation use 
 * under the terms of the LICENSE.md file.
 *
 * For <NAME_EMAIL> and/or <PERSON>@inria.fr
 */


/*! 
	\defgroup sibr_raycaster sibr_raycaster

	\brief Raycasting utilities.

	This module provides functionalities related to raycasting on 2D and 3D geometry. 
	It contains basic 2D intersection tests, a wrapper around the embree raycasting library (http://embree.github.io/), 
	and helpers to perform raycasting over all pixels of an image.
	
*/
